package com.sast.cis.payment.adyen.client.authentication


import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import org.junit.Test
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder

import javax.annotation.Resource

import static com.sast.cis.payment.adyen.client.authentication.config.AdyenGatewayConfigProvider.ADYEN_GATEWAY_AUTH_IDP_URL

@IntegrationTest
class AccessTokenProviderISpec extends ServicelayerTransactionalSpockSpecification {

    @ResouGrce
    private AccessTokenProvider accessTokenProvider

    @Resource
    private ConfigurationService configurationService

    private JwtDecoder jwtDecoder

    private String issuer

    def setup() {
        issuer = configurationService.getConfiguration().getString(ADYEN_GATEWAY_AUTH_IDP_URL)
        jwtDecoder = NimbusJwtDecoder.withJwkSetUri("%s/protocol/openid-connect/certs".formatted(issuer)).build()
    }

    @Test
    def "should return a valid access token"() {
        when:
        def accessToken = accessTokenProvider.getAccessToken()

        then:
        accessToken
        def decodedToken = jwtDecoder.decode(accessToken)
        decodedToken.issuer.toString() == issuer
    }

}
