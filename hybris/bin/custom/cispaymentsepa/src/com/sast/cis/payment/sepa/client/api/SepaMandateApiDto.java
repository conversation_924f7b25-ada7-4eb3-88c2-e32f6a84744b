package com.sast.cis.payment.sepa.client.api;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class SepaMandateApiDto {

    private String iban;
    private LocalDate signatureDate;
    private String accountHolderName;
    private String mandateReference;
    private MandateStatus status;
    private String companyId;
}
