package com.sast.cis.payment.sepa.service;

import com.sast.cis.payment.sepa.client.SepaMandateGatewayApiServiceFactory;
import com.sast.cis.payment.sepa.client.api.SepaMandateApi;
import com.sast.cis.payment.sepa.client.api.SepaMandateApiDto;
import com.sast.cis.payment.sepa.dto.SepaMandateDto;
import com.sast.cis.payment.sepa.tools.SepaMandateConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

/**
 * Service for SEPA mandate operations
 * This service acts as a bridge between the application and the sepa-mandate service,
 * using the SepaMandateGatewayApiServiceFactory to make authenticated REST calls
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SepaMandateService {

    private final SepaMandateGatewayApiServiceFactory sepaMandateGatewayApiServiceFactory;

    public SepaMandateDto createDraftMandate(String companyId) {
        log.info("Creating draft mandate for company: {}", companyId);

        try {
            SepaMandateApi api = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();
            ResponseEntity<SepaMandateApiDto> response = api.createMandate(companyId);

            if (response.getStatusCodeValue() == OK.value() || response.getStatusCodeValue() == CREATED.value()) {
                SepaMandateApiDto apiDto = response.getBody();
                SepaMandateDto mandate = SepaMandateConverter.toDto(apiDto);
                log.info("Successfully created draft mandate with reference: {} for company: {}",
                        mandate.getMandateReference(), companyId);
                return mandate;
            } else {
                log.error("Failed to create draft mandate for company: {}. Status: {}", companyId, response.getStatusCode());
                throw new RuntimeException("Failed to create draft mandate. Status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error creating draft mandate for company: {}", companyId, e);
            throw new RuntimeException("Failed to create draft mandate", e);
        }
    }


    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto mandateDto) {
        log.info("Finalizing draft mandate with reference: {}", reference);

        try {
            SepaMandateApi api = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();
            SepaMandateApiDto payload = SepaMandateConverter.toApiDto(mandateDto);
            ResponseEntity<SepaMandateApiDto> response = api.activateMandate(reference, payload);

            if (response.getStatusCodeValue() == OK.value()) {
                SepaMandateApiDto apiDto = response.getBody();
                SepaMandateDto finalizedMandate = SepaMandateConverter.toDto(apiDto);
                log.info("Successfully finalized mandate with reference: {}", reference);
                return finalizedMandate;
            } else {
                log.error("Failed to finalize mandate with reference: {}. Status: {}", reference, response.getStatusCode());
                throw new RuntimeException("Failed to finalize mandate. Status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error finalizing mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to finalize mandate", e);
        }
    }

}