package com.sast.cis.payment.sepa.client.authentication.filter;

import com.sast.cis.payment.sepa.client.authentication.SepaAccessTokenProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.ws.rs.client.ClientRequestContext;
import javax.ws.rs.client.ClientRequestFilter;

@RequiredArgsConstructor
@Component()
public class SepaAuthenticationFilter implements ClientRequestFilter {

    private final SepaAccessTokenProvider sepaAccessTokenProvider;

    @Override
    public void filter(final ClientRequestContext requestContext) {
        final String token = sepaAccessTokenProvider.getAccessToken();
        requestContext.getHeaders().add("Authorization", "Bearer " + token);
    }
}
