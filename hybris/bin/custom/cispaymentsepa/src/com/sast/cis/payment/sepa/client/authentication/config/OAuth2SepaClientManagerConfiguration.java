package com.sast.cis.payment.sepa.client.authentication.config;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;

import static org.springframework.security.oauth2.core.AuthorizationGrantType.CLIENT_CREDENTIALS;

@Configuration
@RequiredArgsConstructor
public class OAuth2SepaClientManagerConfiguration {

    private final SepaGatewayConfigProvider sepaGatewayConfigProvider;

    @Bean(name = "sepaClientRegistration")
    public ClientRegistration sepaClientRegistration() {
        final SepaMandateConfig.AuthenticationConfig authenticationConfig = sepaGatewayConfigProvider.getConfig().authenticationConfig();
        return ClientRegistration
            .withRegistrationId(authenticationConfig.clientRegistrationId())
            .tokenUri(authenticationConfig.tokenEndpointUrl())
            .clientId(authenticationConfig.clientId())
            .clientSecret(authenticationConfig.clientSecret())
            .authorizationGrantType(CLIENT_CREDENTIALS)
            .build();
    }

    @Bean(name = "sepaClientRegistrationRepository")
    public ClientRegistrationRepository sepaClientRegistrationRepository(
            @Qualifier("sepaClientRegistration") final ClientRegistration sepaClientRegistration) {
        return new InMemoryClientRegistrationRepository(sepaClientRegistration);
    }

    @Bean(name = "sepaAuthorizedClientService")
    public OAuth2AuthorizedClientService sepaAuthorizedClientService(
            @Qualifier("sepaClientRegistrationRepository") final ClientRegistrationRepository sepaClientRegistrationRepository) {
        return new InMemoryOAuth2AuthorizedClientService(sepaClientRegistrationRepository);
    }

    @Bean(name = "sepaAuthorizedClientManager")
    public AuthorizedClientServiceOAuth2AuthorizedClientManager sepaAuthorizedClientManager(
        @Qualifier("sepaClientRegistrationRepository") final ClientRegistrationRepository sepaClientRegistrationRepository,
        @Qualifier("sepaAuthorizedClientService") final OAuth2AuthorizedClientService sepaAuthorizedClientService) {

        return new AuthorizedClientServiceOAuth2AuthorizedClientManager(sepaClientRegistrationRepository, sepaAuthorizedClientService);
    }
}
