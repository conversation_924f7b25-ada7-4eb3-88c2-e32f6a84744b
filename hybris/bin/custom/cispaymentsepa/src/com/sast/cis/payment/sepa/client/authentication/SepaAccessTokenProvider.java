package com.sast.cis.payment.sepa.client.authentication;

import com.sast.cis.payment.sepa.client.authentication.config.SepaGatewayConfigProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.oauth2.client.OAuth2AuthorizeRequest;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component("sepaAccessTokenProvider")
public class SepaAccessTokenProvider {

    @Qualifier("sepaAuthorizedClientManager")
    private final OAuth2AuthorizedClientManager oAuth2AuthorizedClientManager;
    private final SepaGatewayConfigProvider sepaGatewayConfigProvider;

    public String getAccessToken() {
        final String clientRegistrationId = sepaGatewayConfigProvider.getConfig().authenticationConfig().clientRegistrationId();
        final OAuth2AuthorizeRequest authorizeRequest = OAuth2AuthorizeRequest
            .withClientRegistrationId(clientRegistrationId)
            .principal("system")
            .build();

        final OAuth2AuthorizedClient authorizedClient = oAuth2AuthorizedClientManager.authorize(authorizeRequest);

        if (authorizedClient == null || authorizedClient.getAccessToken() == null) {
            throw new IllegalStateException("Failed to obtain access token for client: " + clientRegistrationId);
        }

        return authorizedClient.getAccessToken().getTokenValue();
    }
}
