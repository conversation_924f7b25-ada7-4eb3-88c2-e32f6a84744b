<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans.xsd
            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.sast.cis.payment.sepa"/>

    <import resource="classpath*:/servicelayer-spring.xml"/>

    <!-- Service Bean -->
    <bean id="sepaMandateService"
          class="com.sast.cis.payment.sepa.service.SepaMandateService">
        <constructor-arg ref="sepaMandateGatewayApiServiceFactory"/>
    </bean>

</beans>